import LocalAreasLandingPage from '@/components/LocalAreasLandingPage';
import { slicesFetch } from '@/slices/fetch';
import { PageMode, SlicesContextData } from '@/types/common';
import {
  formatDistrictName,
  getSliceDataObject,
  returnMetaDataRobots,
} from '@/utils/helpers';
import { KeyTextField } from '@prismicio/client';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { LandingPageDocument } from 'prismicio-types';
import { createClient } from '../../../prismicio';
import { LONDON_DISTRICTS } from '@/utils/constants';

interface PageProps {
  params: {
    district: string;
  };
  searchParams: { mode: PageMode };
}

async function getData(
  district: string,
  searchParams?: Record<string, string>
) {
  if (!LONDON_DISTRICTS.includes(district)) {
    notFound();
  }

  try {
    const client = createClient();

    const landingPage = await client
      .getByUID('landing_page', 'local-area')
      .catch(() => undefined);

    const page = landingPage as LandingPageDocument<string>;

    if (!searchParams) {
      return {
        props: { page },
      };
    }

    const slicesData = await getSliceDataObject(
      page.data.slices,
      slicesFetch,
      searchParams
    );

    const headerAnchorItems: KeyTextField[] = page.data.slices.map(
      ({ primary }) =>
        'header_anchor_name' in primary ? primary?.header_anchor_name : null
    );

    return {
      props: {
        page,
        headerAnchorItems,
        slicesData: slicesData,
      },
    };
  } catch (error) {
    notFound();
  }
}

export async function generateMetadata({
  params,
  searchParams,
}: PageProps): Promise<Metadata> {
  const district = formatDistrictName(params.district);
  const { props } = await getData(district);

  if (searchParams.mode === 'commercial') {
    return {
      title: props.page.data.title_commercial,
      description: props.page.data.description_commercial,
      robots: returnMetaDataRobots({
        noFollow: props.page.data.no_follow_commercial,
        noIndex: props.page.data.no_index_commercial,
      }),
      openGraph: {
        title: String(props.page.data.title_commercial),
        description: String(props.page.data.description_commercial),
        images: [props.page.data.image_commercial?.url ?? ''],
      },
    };
  }

  return {
    title: props.page.data.title_residential,
    description: props.page.data.description_residential,
    robots: returnMetaDataRobots({
      noFollow: props.page.data.no_follow_residential,
      noIndex: props.page.data.no_index_residential,
    }),
    openGraph: {
      title: String(props.page.data.title_residential),
      description: String(props.page.data.description_residential),
      images: [props.page.data.image_residential?.url ?? ''],
    },
  };
}

export default async function Page({ params, searchParams }: PageProps) {
  const district = formatDistrictName(params.district);
  const { props } = await getData(district, searchParams);

  return (
    <LocalAreasLandingPage
      searchParams={searchParams}
      page={props.page}
      slicesData={district as any as SlicesContextData}
    />
  );
}
