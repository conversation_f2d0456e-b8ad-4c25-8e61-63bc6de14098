import { breakpoints } from '@/styles/constants.css';
import { theme } from '@/styles/themes.css';
import { style } from '@vanilla-extract/css';

export const root = style({
  padding: '20px 10px',
  display: 'flex',
  gap: 40,
  justifyContent: 'center',
  '@media': {
    [breakpoints.tablet]: {
      padding: '20px 60px',
    },
    [breakpoints.desktop]: {
      justifyContent: 'initial',
    },
  },
});

export const imageDesktop = style({
  display: 'none',
  '@media': {
    [breakpoints.desktop]: {
      display: 'block',
      width: 515,
      height: 'auto',
      objectFit: 'cover',
      borderRadius: 24,
    },
  },
});

export const imageMobile = style({
  display: 'block',
  width: '100%',
  height: 'auto',
  objectFit: 'cover',
  borderRadius: 24,
  '@media': {
    [breakpoints.desktop]: {
      display: 'none',
    },
  },
});

export const contentWrapper = style({
  display: 'flex',
  flexDirection: 'column',
  gap: 24,
  maxWidth: 725,
  '@media': {
    [breakpoints.tablet]: {
      gap: 30,
    },
  },
});

export const title = style({
  maxWidth: 320,
  '@media': {
    [breakpoints.tablet]: {
      maxWidth: 699,
    },
  },
});

export const list = style({
  padding: 0,
  paddingLeft: 0,
  display: 'flex',
  flexDirection: 'column',
  gap: 10,
});

export const listItem = style({
  display: 'flex',
  gap: 14,
});

export const listItemIcon = style({
  fontSize: 20,
});

export const listItemText = style({
  fontSize: 18,
  '@media': {
    [breakpoints.tablet]: {
      fontSize: 20,
    },
  },
});

export const buttonsWrapper = style({
  display: 'flex',
  gap: 20,
  justifyContent: 'space-between',
  flexWrap: 'wrap',
});

export const btn = style({
  flex: 1,
});

export const hiddenOnMobile = style({
  display: 'none',
  '@media': {
    [breakpoints.tablet]: {
      display: 'block',
    },
  },
});

export const cardWrapper = style({
  padding: '20px 10px',
  '@media': {
    [breakpoints.tablet]: {
      padding: 20,
    },
  },
});

export const card = style({
  padding: 20,
  borderRadius: 24,
  backgroundColor: theme.colors.primary.asidGreen,
  display: 'flex',
  flexWrap: 'wrap',
  gap: 20,
  '@media': {
    [breakpoints.tablet]: {
      padding: 40,
      gap: 60,
      flexWrap: 'nowrap',
    },
  },
});

export const cardTextWrapper = style({
  display: 'flex',
  flexDirection: 'column',
  gap: 20,
});

export const titleGreenCard = style({
  maxWidth: 264,
  '@media': {
    [breakpoints.tablet]: {
      maxWidth: 500,
    },
  },
});
