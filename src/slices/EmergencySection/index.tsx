import classNames from 'classnames';
import Container from '@/components/Container';
import { PrismicNextImage } from '@prismicio/next';
import { Content } from '@prismicio/client';
import { PrismicRichText, SliceComponentProps } from '@prismicio/react';
import Typography from '@/components/Typography';
import MemoCheckMarkIcon from '@/assets/icons/CheckMarkIcon';
import {
  CallButtonSecondary,
  WhatsAppButtonOutlined,
} from '@/components/ContactButtons';
import * as styles from './EmergencySection.css';

/**
 * Props for `EmergencySection`.
 */
export type EmergencySectionProps =
  SliceComponentProps<Content.EmergencySectionSlice>;

/**
 * Component for "EmergencySection" Slices.
 */
const EmergencySection = ({ slice }: EmergencySectionProps): JSX.Element => {
  return (
    <Container removeBorderRadius>
      <section
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <div className={styles.root}>
          <PrismicNextImage
            field={slice.primary.image}
            className={styles.imageDesktop}
          />

          <div className={styles.contentWrapper}>
            <div>
              <Typography
                variant='h3'
                fontFamily='primary'
                className={classNames(styles.title, styles.hiddenOnMobile)}
              >
                <PrismicRichText field={slice.primary.title_1} />
              </Typography>
              <Typography
                variant='h3'
                fontFamily='primary'
                className={styles.title}
              >
                <PrismicRichText field={slice.primary.title_2} />
              </Typography>
            </div>

            <PrismicNextImage
              field={slice.primary.image}
              className={styles.imageMobile}
            />
            <Typography variant='subTitleMedium'>
              <PrismicRichText field={slice.primary.subtitle} />
            </Typography>
            <Typography variant='buttonMedium' style={{ fontWeight: 400 }}>
              <PrismicRichText field={slice.primary.description} />
            </Typography>

            <ul className={styles.list}>
              {slice.items.map(({ list_primary }, idx) => {
                return (
                  <li
                    key={`${idx}_emergency_section_list_item`}
                    className={styles.listItem}
                  >
                    <MemoCheckMarkIcon className={styles.listItemIcon} />
                    <Typography className={styles.listItemText}>
                      <PrismicRichText field={list_primary} />
                    </Typography>
                  </li>
                );
              })}
            </ul>

            <div className={styles.buttonsWrapper}>
              <CallButtonSecondary
                isOutlineIcon
                text='Call Us'
                className={styles.btn}
              />
              <WhatsAppButtonOutlined className={styles.btn} />
            </div>
          </div>
        </div>

        <div className={styles.cardWrapper}>
          <div className={styles.card}>
            <Typography
              variant='h3'
              fontFamily='primary'
              className={styles.titleGreenCard}
            >
              <PrismicRichText
                field={slice.primary.title_green_card}
                components={{
                  strong: ({ children }) => (
                    <span style={{ fontWeight: 700 }}>{children}</span>
                  ),
                }}
              />
            </Typography>

            <div className={styles.cardTextWrapper}>
              <Typography variant='buttonMedium' style={{ fontWeight: 400 }}>
                <PrismicRichText
                  field={slice.primary.description_green_card}
                  components={{
                    strong: ({ children }) => (
                      <span style={{ fontWeight: 700 }}>{children}</span>
                    ),
                  }}
                />
              </Typography>

              <ul className={styles.list}>
                {slice.items.map(({ list_secondary }, idx) => {
                  return (
                    <li
                      key={`${idx}_emergency_section_list_item`}
                      className={styles.listItem}
                    >
                      <MemoCheckMarkIcon className={styles.listItemIcon} />
                      <Typography className={styles.listItemText}>
                        <PrismicRichText field={list_secondary} />
                      </Typography>
                    </li>
                  );
                })}
              </ul>

              <Typography variant='buttonMedium' style={{ fontWeight: 400 }}>
                <PrismicRichText
                  field={slice.primary.description_2_green_card}
                />
              </Typography>
            </div>
          </div>
        </div>
      </section>
    </Container>
  );
};

export default EmergencySection;
