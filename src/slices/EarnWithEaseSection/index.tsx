import { Content } from '@prismicio/client';
import { PrismicRichText, SliceComponentProps } from '@prismicio/react';
import { PrismicNextImage } from '@prismicio/next';
import Button from '@/components/Button';
import Typography from '@/components/Typography';
import Container from '@/components/Container';
import * as styles from './EarnWithEaseSection.css';

/**
 * Props for `EarnWithEaseSection`.
 */
export type EarnWithEaseSectionProps =
  SliceComponentProps<Content.EarnWithEaseSectionSlice> & {
    context: string;
  };

/**
 * Component for "EarnWithEaseSection" Slices.
 */
const EarnWithEaseSection = ({
  slice,
  context,
}: EarnWithEaseSectionProps): JSX.Element => {
  return (
    <Container removeBorderRadius>
      <section
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={styles.root}
      >
        <div className={styles.imgWrapper}>
          <Typography
            variant='h3'
            fontFamily='primary'
            className={styles.title}
          >
            <PrismicRichText
              field={slice.primary.title}
              components={{
                strong: ({ children }) => (
                  <span style={{ fontWeight: 700 }}>{children}</span>
                ),
              }}
            />
            <PrismicRichText
              field={slice.primary.subtitle}
              components={{
                paragraph: ({ text }) => {
                  const t = text ?? '';
                  return <span>{t.replace('{district}', context)}</span>;
                },
              }}
            />
          </Typography>

          <PrismicNextImage
            field={slice.primary.image}
            className={styles.img}
          />
        </div>

        <div className={styles.infoWrapper}>
          <div className={styles.description}>
            <Typography variant='buttonMedium' style={{ fontWeight: 400 }}>
              <span style={{ fontWeight: 700 }}>
                Know someone in{' '}
                <span style={{ textTransform: 'capitalize' }}>{context}</span>{' '}
                or anywhere else in London,
              </span>{' '}
              who needs a new boiler?
            </Typography>

            <div className={styles.greenCard}>
              <Typography variant='buttonMedium' style={{ fontWeight: 400 }}>
                Refer them to us and{' '}
                <span style={{ fontWeight: 700 }}>
                  we’ll thank you with £100 straight into your bank account.
                </span>
              </Typography>
            </div>
          </div>
          <div className={styles.whiteCard}>
            <Typography
              variant='buttonMedium'
              style={{ fontWeight: 400, marginBottom: 16 }}
            >
              But it doesn’t stop there.
            </Typography>
            <Typography
              variant='buttonMedium'
              style={{ fontWeight: 400, marginBottom: 20 }}
            >
              <span style={{ fontWeight: 700 }}>
                We also offer referral rewards for a wide range of services –{' '}
              </span>
              from plumbing repairs to heating installations and even commercial
              projects.
            </Typography>
            <Typography variant='buttonMedium' style={{ fontWeight: 400 }}>
              Whether it’s a neighbour, client, or friend, if they need our
              help, you can earn just by sending them our way.
            </Typography>
          </div>
          <div className={styles.memberTextWrapper}>
            <div className={styles.greyCard}>
              <Typography
                variant='buttonMedium'
                style={{ display: 'block', fontWeight: 400, marginBottom: 10 }}
              >
                No forms. No hassle.{' '}
                <span style={{ fontWeight: 700 }}>Just rewards</span> for
                helping others find a trusted local plumbing firm in{' '}
                <span style={{ textTransform: 'capitalize' }}>{context}</span>.
              </Typography>
              <Typography variant='buttonMedium' style={{ fontWeight: 400 }}>
                <span style={{ fontWeight: 700 }}> Become a Member </span> to
                Join our Earn Programm
              </Typography>
            </div>

            <Button isAnimated style={{ width: '100%' }}>
              <span className={styles.buttonText}>
                Become a Member – Earn Now
              </span>
              <span className={styles.buttonTextMobile}>Become a Member</span>
            </Button>
          </div>
        </div>
      </section>
    </Container>
  );
};

export default EarnWithEaseSection;
