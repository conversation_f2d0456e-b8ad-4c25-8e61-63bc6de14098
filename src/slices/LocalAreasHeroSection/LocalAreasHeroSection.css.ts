import { breakpoints } from '@/styles/constants.css';
import { theme } from '@/styles/themes.css';
import { style } from '@vanilla-extract/css';

export const container = style({
  padding: 20,

  '@media': {
    [breakpoints.tablet]: {
      padding: 60,
    },
  },
});

export const mainWrapper = style({
  flexWrap: 'wrap',
  display: 'flex',

  '@media': {
    [breakpoints.tablet]: {
      justifyContent: 'space-between',
      gap: 30,
      flexWrap: 'nowrap',
    },
  },
});

export const leftWrapper = style({
  maxWidth: '792px',
  flexGrow: 1,
  order: 2,

  '@media': {
    [breakpoints.tablet]: {
      order: 1,
    },
  },
});

export const contactsWrapper = style({
  margin: '20px 0',
  display: 'flex',
  gap: 20,
  flexWrap: 'wrap',
  width: '100%',

  '@media': {
    [breakpoints.tablet]: {
      margin: '0 0 40px 0',
    },
  },
});

export const title = style({
  display: 'none',
  '@media': {
    [breakpoints.tablet]: {
      display: 'block',
      fontSize: 64,
      WebkitTextStroke: '2px #000',
    },
    [breakpoints.desktop]: {
      fontSize: 88,
    },
  },
});

export const mobileTitle = style({
  display: 'block',
  position: 'absolute',
  top: 20,
  left: 20,
  fontSize: 44,
  lineHeight: '95%',
  letterSpacing: '-0.88px',
  color: theme.colors.primary.softWhite,

  '@media': {
    [breakpoints.tablet]: {
      display: 'none',
    },
  },
});

export const gradientOverlay = style({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '90%',
  borderRadius: 24,
  background:
    'linear-gradient(180deg, rgba(0, 61, 35, 0.90) 0%, rgba(0, 61, 35, 0.00) 100%)',

  '@media': {
    [breakpoints.tablet]: {
      display: 'none',
    },
  },
});

export const description = style({
  backgroundColor: '#fff',
  padding: '24px',
  borderRadius: '24px',
  fontSize: '20px',

  '@media': {
    [breakpoints.tablet]: {
      padding: 0,
      backgroundColor: 'transparent',
    },
  },
});

export const accentDescriptionPart = style({
  fontWeight: 700,
  fontSize: 21,
  '@media': {
    [breakpoints.tablet]: {
      fontWeight: 400,
      fontSize: 20,
    },
  },
});

export const btn = style({
  width: '100%',
  minWidth: 0,
  '@media': {
    [breakpoints.tablet]: {
      width: 'auto',
      minWidth: 230,
    },
  },
});

export const brandLabel = style({
  '@media': {
    [breakpoints.tablet]: {
      display: 'none !important',
    },
  },
});

export const districtName = style({
  textTransform: 'capitalize',
  fontWeight: 700,
});

export const imageWrapper = style({
  position: 'relative',
  width: '100%',
  marginTop: 20,
  objectFit: 'cover',
  order: 1,
  minWidth: 0,
  '@media': {
    [breakpoints.tablet]: {
      minWidth: 300,
      marginTop: 0,
      height: '100%',
      maxHeight: '100%',
      order: 2,
    },
  },
});

export const image = style({
  width: '100%',
  borderRadius: 24,
  objectFit: 'cover',
});

export const list = style({
  display: 'none',
  '@media': {
    [breakpoints.tablet]: {
      margin: '40px 0',
      paddingLeft: 0,
      display: 'flex',
      flexDirection: 'column',
      gap: 10,
    },
  },
});

export const listItem = style({
  display: 'flex',
  alignItems: 'center',
  gap: 14,
});

export const listItemIcon = style({
  fontSize: 29,
});

export const listItemText = style({
  fontSize: 24,
  height: '20px',
});

export const callButtonIcon = style({
  fontSize: 24,
  color: theme.colors.primary.castletonGreen,
});
