import Link from 'next/link';
import { Content } from '@prismicio/client';
import {
  PrismicImage,
  PrismicRichText,
  SliceComponentProps,
} from '@prismicio/react';
import Container from '@/components/Container';
import Typography from '@/components/Typography';
import MemoCheckMarkIcon from '@/assets/icons/CheckMarkIcon';
import Button from '@/components/Button';
import {
  CallButtonSecondary,
  WhatsAppButtonOutlined,
} from '@/components/ContactButtons';
import BrandLabel from '@/components/BrandLabel';
import { theme } from '@/styles/themes.css';
import * as styles from './LocalAreasHeroSection.css';

export type LocalAreasHeroSectionProps =
  SliceComponentProps<Content.LocalAreasHeroSectionSlice> & {
    context: string;
  };

const LocalAreasHeroSection = ({
  slice,
  context,
}: LocalAreasHeroSectionProps): JSX.Element => {
  return (
    <Container removeBorderRadius className={styles.container}>
      <BrandLabel className={styles.brandLabel} />
      <section
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={styles.mainWrapper}
      >
        <div className={styles.leftWrapper}>
          <Typography
            variant='h1'
            fontFamily='primary'
            className={styles.title}
          >
            <PrismicRichText
              field={slice.primary.title}
              components={{
                paragraph: ({ text }) => {
                  const t = text ?? '';
                  const updatedText = t.replace('{district}', '');
                  return (
                    <span style={{ fontWeight: 400 }}>
                      {updatedText}{' '}
                      <span className={styles.districtName}>{context}</span>
                    </span>
                  );
                },
              }}
            />
          </Typography>

          <ul className={styles.list}>
            {slice.items.map(({ point }, idx) => {
              return (
                <li
                  key={`${idx}_hero_section_list_item`}
                  className={styles.listItem}
                >
                  <MemoCheckMarkIcon className={styles.listItemIcon} />
                  <Typography className={styles.listItemText}>
                    <PrismicRichText
                      field={point}
                      components={{
                        paragraph: ({ text }) => {
                          const t = text ?? '';
                          return (
                            <span>{t.replace('{district}', context)}</span>
                          );
                        },
                      }}
                    />
                  </Typography>
                </li>
              );
            })}
          </ul>

          <div className={styles.contactsWrapper}>
            <CallButtonSecondary className={styles.btn} />
            <WhatsAppButtonOutlined className={styles.btn} />
            <Button
              variant='outlined'
              color='secondary'
              as={Link}
              href='/'
              style={{
                backgroundColor: theme.colors.primary.castletonGreen,
                color: theme.colors.primary.softWhite,
                minWidth: 230,
              }}
              className={styles.btn}
            >
              Get a Boiler Quote
            </Button>
          </div>

          <Typography variant='bodyMedium' className={styles.description}>
            <PrismicRichText
              field={slice.primary.description}
              components={{
                strong: ({ children }) => (
                  <span className={styles.accentDescriptionPart}>
                    {children}
                  </span>
                ),
              }}
            />
          </Typography>
        </div>

        <div className={styles.imageWrapper}>
          <div className={styles.gradientOverlay} />
          <Typography
            variant='h1'
            fontFamily='primary'
            className={styles.mobileTitle}
          >
            <PrismicRichText
              field={slice.primary.title}
              components={{
                paragraph: ({ text }) => {
                  const t = text ?? '';
                  const updatedText = t.replace('{district}', '');
                  return (
                    <span>
                      {updatedText}{' '}
                      <span className={styles.districtName}>{context}</span>
                    </span>
                  );
                },
              }}
            />
          </Typography>

          <PrismicImage field={slice.primary.hero} className={styles.image} />
        </div>
      </section>
    </Container>
  );
};

export default LocalAreasHeroSection;
