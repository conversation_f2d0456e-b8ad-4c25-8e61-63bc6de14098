'use client';
import Typography from '@/components/Typography';
import { Content } from '@prismicio/client';
import { SliceComponentProps } from '@prismicio/react';
import { Controller, useForm } from 'react-hook-form';
import useStore from '@/hooks/useStore';
import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import {
  EMAIL_CONTROLLER_RULES,
  EMAIL_INPUT_PROPS,
  PHONE_NUMBER_CONTROLLER_RULES,
  PHONE_NUMBER_INPUT_PROPS,
  POST_CODE_CONTROLLER_RULES,
  POST_CODE_INPUT_PROPS,
} from '@/utils/constants';
import TextInput from '@/components/TextInput';
import { PatternFormat } from 'react-number-format';
import WrapperWithLabel from '@/components/WrapperWithLabel';
import CheckButton from '@/components/CheckButton';
import Button from '@/components/Button';
import Container from '@/components/Container';
import * as styles from './ContactFormSection.css';

interface FormValues {
  Name: string;
  phone: string;
  Email: string;
  post_code: string;
  urgent: boolean;
  respond_types: string[];
  message?: string;
  ref_id?: string;
  service?: string;
}

const RESPOND_VARIANTS = {
  callMe: 'Phone Call',
  whatsAppMe: 'WhatsApp',
  textMe: 'Text message',
  emailMe: 'Email',
};

export type ContactFormSectionProps =
  SliceComponentProps<Content.ContactFormSectionSlice>;

const ContactFormSection = ({
  slice,
}: ContactFormSectionProps): JSX.Element => {
  const { auth } = useStore();
  const [error, setError] = useState<string | null>(null);

  const form = useForm<FormValues>({
    mode: 'onBlur',
    defaultValues: {
      urgent: true,
      Name: '',
      Email: '',
      phone: '',
      post_code: '',
      message: '',
      respond_types: [],
      ref_id: '',
      service: 'New boiler installation',
    },
  });

  const onSubmit = async (body: FormValues) => {
    try {
      const supabase = createClient();

      if (body.ref_id === '') delete body.ref_id;

      const response = await supabase.functions.invoke('zoho_api/bookings', {
        method: 'POST',
        body,
      });

      if (response.data.error) {
        throw response.data.error.message;
      }

      form.reset();
    } catch (error) {
      setError(String(error));
    }
  };

  useEffect(() => {
    if (!auth.isAuthorized || !auth.user) return;

    form.reset({
      urgent: true,
      Name: auth.user.full_name,
      Email: auth.user.email,
      phone: auth.user.mobile_number,
      post_code: auth.user.post_code,
      message: '',
      respond_types: [],
      ref_id: auth.user.ref_by?.ref_id ?? '',
      service: 'New boiler installation',
    });
  }, [auth.user, auth.isAuthorized]);

  return (
    <Container removeBorderRadius>
      <section
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={styles.root}
      >
        <Typography variant='h3' fontFamily='primary' className={styles.title}>
          Message Us
        </Typography>

        <form className={styles.formWrapper}>
          <div>
            <Typography variant='subTitleMedium'>Contact Form</Typography>
            <Typography
              variant='subTitleSmall'
              style={{ marginTop: 6, opacity: 0.8 }}
            >
              If you have any questions, save them until the end of this form
              and our team will answer everything for you
            </Typography>
          </div>

          <div className={styles.formGrid}>
            <div>
              <Controller
                rules={{
                  required: 'Required!',
                  minLength: {
                    value: 3,
                    message: 'At least 3 characters required',
                  },
                  maxLength: {
                    value: 50,
                    message: 'Max 50 characters',
                  },
                }}
                name='Name'
                control={form.control}
                render={({ field, fieldState }) => (
                  <TextInput
                    {...{
                      label: 'Name',
                    }}
                    {...field}
                    className={styles.field}
                    error={fieldState.error?.message}
                  />
                )}
              />

              <Controller
                rules={{
                  required: 'Required!',
                  ...PHONE_NUMBER_CONTROLLER_RULES,
                }}
                name='phone'
                control={form.control}
                render={({ field, fieldState }) => (
                  <PatternFormat
                    {...PHONE_NUMBER_INPUT_PROPS}
                    className={styles.field}
                    error={fieldState.error?.message}
                    {...field}
                    customInput={TextInput}
                    placeholder=''
                  />
                )}
              />

              <Controller
                rules={{
                  required: 'Required!',
                  ...EMAIL_CONTROLLER_RULES,
                }}
                name='Email'
                control={form.control}
                render={({ field, fieldState }) => (
                  <TextInput
                    {...EMAIL_INPUT_PROPS}
                    {...field}
                    className={styles.field}
                    error={fieldState.error?.message}
                    placeholder=''
                  />
                )}
              />

              <Controller
                rules={{
                  required: 'Required!',
                  ...POST_CODE_CONTROLLER_RULES,
                }}
                name='post_code'
                control={form.control}
                render={({ field, fieldState }) => (
                  <TextInput
                    {...POST_CODE_INPUT_PROPS}
                    {...field}
                    maxLength={7}
                    error={fieldState.error?.message}
                    placeholder=''
                  />
                )}
              />
            </div>

            <div className={styles.textareaWrapper}>
              <Controller
                rules={{
                  required: 'Required!',
                  minLength: {
                    value: 10,
                    message: 'At least 10 characters required',
                  },
                }}
                name='message'
                control={form.control}
                render={({ field, fieldState }) => (
                  <TextInput
                    {...field}
                    error={fieldState.error?.message}
                    label='How Can We Help?'
                    placeholder=''
                    type='text'
                    element='textarea'
                    style={{ minHeight: 225 }}
                  />
                )}
              />

              <div style={{ paddingBottom: 2 }}>
                <WrapperWithLabel
                  className={styles.radioButtonWrapper}
                  label='How do you want us to respond to your query?'
                >
                  {Object.values(RESPOND_VARIANTS).map((label) => (
                    <Controller
                      key={label}
                      name='respond_types'
                      control={form.control}
                      render={({ field }) => (
                        <CheckButton
                          className={styles.checkButton}
                          {...field}
                          value={field.value.includes(label)}
                          onChange={() => {
                            const newValue = field.value.includes(label)
                              ? field.value.filter((lab) => lab !== label)
                              : [...field.value, label];
                            field.onChange(newValue);
                          }}
                        >
                          {label}
                        </CheckButton>
                      )}
                    />
                  ))}
                </WrapperWithLabel>
              </div>
            </div>
          </div>

          <Button
            type='button'
            onClick={form.handleSubmit(onSubmit)}
            isAnimated
            disabled={
              !form.formState.isValid ||
              form.formState.isLoading ||
              form.formState.isSubmitting
            }
            className={styles.submitButton}
          >
            Send
          </Button>
        </form>
      </section>
    </Container>
  );
};

export default ContactFormSection;
