import { breakpoints } from '@/styles/constants.css';
import { theme } from '@/styles/themes.css';
import { style } from '@vanilla-extract/css';

export const root = style({
  padding: 20,
  backgroundColor: theme.colors.primary.castletonGreen,
  borderRadius: 24,
  position: 'relative',
  display: 'flex',
  justifyContent: 'space-between',
  flexWrap: 'wrap',
  '@media': {
    [breakpoints.tablet]: {
      padding: 40,
      gap: 20,
    },
  },
});

export const container = style({
  paddingTop: 20,
  paddingBottom: 20,
});

export const title = style({
  color: theme.colors.primary.softWhite,
  width: 274,
  marginBottom: 40,
  '@media': {
    [breakpoints.tablet]: {
      marginBottom: 0,
      width: 474,
    },
  },
});

export const district = style({
  textTransform: 'capitalize',
  fontWeight: 700,
});

export const cardsWrapper = style({
  display: 'flex',
  flexWrap: 'wrap',
  gap: 20,
  color: theme.colors.primary.softWhite,
  zIndex: 2,
  '@media': {
    [breakpoints.tablet]: {
      gap: 40,
      width: 700,
    },
  },
});

export const card = style({
  padding: 24,
  borderRadius: 16,
  backgroundColor: '#195038',
  display: 'flex',
  flexDirection: 'column',
  gap: 16,
  width: '100%',
  '@media': {
    [breakpoints.tablet]: {
      minHeight: 380,
      width: 330,
    },
  },
});

export const primaryCard = style({
  padding: 24,
  borderRadius: 16,
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  display: 'flex',
  flexDirection: 'column',
  width: '100%',
  '@media': {
    [breakpoints.tablet]: {
      width: 330,
      minHeight: 380,
    },
  },
});

export const fullWidthCardContentWrapper = style({
  display: 'flex',
  flexWrap: 'wrap',
  gap: 16,
  '@media': {
    [breakpoints.tablet]: {
      gap: 32,
      flexWrap: 'nowrap',
    },
  },
});
export const fullWidthCard = style({
  minHeight: '0 !important',
  width: '100% !important',
});

export const primaryCardTitle = style({
  marginBottom: 32,
  fontSize: '44px !important',
  fontWeight: 400,
  lineHeight: '95%',
  letterSpacing: '-0.88px',
});

export const image = style({
  width: '100%',
  maxWidth: '100%',
  height: 'auto',
  maxHeight: 170,
  objectFit: 'cover',
  borderRadius: 24,
  '@media': {
    [breakpoints.tablet]: {
      width: '282px',
    },
  },
});

export const callBtn = style({
  minWidth: '0px !important',
  marginTop: 16,
  color: `${theme.colors.primary.asidGreen} !important`,
  backgroundColor: `${theme.colors.primary.castletonGreen} !important`,
  width: '100%',
  ':hover': {
    backgroundColor: `${theme.colors.primary.softWhite} !important`,
    color: `${theme.colors.primary.castletonGreen} !important`,
  },
  '@media': {
    [breakpoints.tablet]: {
      marginTop: 'auto',
    },
  },
});

export const secondaryText = style({
  color: theme.colors.primary.softWhite,
  opacity: 0.8,
});

export const caption = style({
  color: 'rgba(255, 253, 248, 0.50)',
  opacity: 0.8,
});

export const overlayImage = style({
  width: '60%',
  height: 'auto',
  position: 'absolute',
  bottom: 0,
  left: 0,
  zIndex: 1,
  display: 'none',
  '@media': {
    [breakpoints.desktop]: {
      display: 'block',
    },
  },
});
