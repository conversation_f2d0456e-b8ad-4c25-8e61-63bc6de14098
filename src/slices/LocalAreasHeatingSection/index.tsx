import classNames from 'classnames';
import Container from '@/components/Container';
import { Content } from '@prismicio/client';
import {
  PrismicImage,
  PrismicRichText,
  SliceComponentProps,
} from '@prismicio/react';
import Typography from '@/components/Typography';
import { CallButtonSecondary } from '@/components/ContactButtons';
import Button from '@/components/Button';
import * as styles from './LocalAreasHeatingSection.css';

/**
 * Props for `LocalAreasHeatingSection`.
 */
export type LocalAreasHeatingSectionProps =
  SliceComponentProps<Content.LocalAreasHeatingSectionSlice> & {
    context: string;
  };

/**
 * Component for "LocalAreasHeatingSection" Slices.
 */
const LocalAreasHeatingSection = ({
  slice,
  context,
}: LocalAreasHeatingSectionProps): JSX.Element => {
  return (
    <Container removeBorderRadius className={styles.container}>
      <section
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={styles.root}
      >
        <PrismicImage
          field={slice.primary.overlay_image}
          className={styles.overlayImage}
        />
        <Typography
          variant='h3'
          fontFamily='primary'
          as='h2'
          className={styles.title}
        >
          <PrismicRichText
            field={slice.primary.main_title}
            components={{
              paragraph: ({ text }) => {
                const t = text ?? '';
                const updatedText = t.replace('{district}', '');
                return (
                  <span>
                    {updatedText}{' '}
                    <span className={styles.district}>{context}</span>
                  </span>
                );
              },
            }}
          />
        </Typography>

        <div className={styles.cardsWrapper}>
          <div className={styles.primaryCard}>
            <Typography
              variant='h3'
              fontFamily='primary'
              as='h3'
              className={styles.primaryCardTitle}
            >
              <PrismicRichText field={slice.primary.card1_title} />
            </Typography>
            <Typography variant='subTitleMedium' style={{ marginBottom: 16 }}>
              <PrismicRichText field={slice.primary.card1_subtitle} />
            </Typography>
            <Typography
              variant='buttonMedium'
              style={{ fontWeight: 400, opacity: 0.8 }}
            >
              <PrismicRichText field={slice.primary.card1_description} />
            </Typography>

            <CallButtonSecondary
              text='Get Instant Help'
              className={styles.callBtn}
            />
          </div>

          <div className={styles.card}>
            <Typography variant='subTitleMedium' style={{ lineHeight: '80%' }}>
              <PrismicRichText field={slice.primary.card2_title} />
            </Typography>

            <PrismicImage
              field={slice.primary.card2_image}
              className={styles.image}
            />
            <Button>Book Now</Button>
            <Typography variant='subTitleSmall' className={styles.caption}>
              <PrismicRichText field={slice.primary.card2_description} />
            </Typography>
          </div>

          <div className={styles.card}>
            <Typography variant='subTitleMedium' style={{ lineHeight: '80%' }}>
              <PrismicRichText field={slice.primary.card3_title} />
            </Typography>
            <PrismicImage
              field={slice.primary.card3_image}
              className={styles.image}
            />
            <Button>Fix Your Problem</Button>
          </div>

          <div className={styles.card}>
            <Typography variant='subTitleMedium' style={{ lineHeight: '80%' }}>
              <PrismicRichText
                field={slice.primary.card4_title}
                components={{
                  paragraph: ({ text }) => {
                    const t = text ?? '';
                    return <span>{t.replace('{district}', context)}</span>;
                  },
                }}
              />
            </Typography>
            <PrismicImage
              field={slice.primary.card4_image}
              className={styles.image}
            />
            <Button>Book Now Save 10%</Button>
          </div>

          <div className={classNames(styles.card, styles.fullWidthCard)}>
            <Typography variant='subTitleMedium' style={{ lineHeight: '80%' }}>
              <PrismicRichText
                field={slice.primary.card5_title}
                components={{
                  paragraph: ({ text }) => {
                    const t = text ?? '';
                    return <span>{t.replace('{district}', context)}</span>;
                  },
                }}
              />
            </Typography>

            <div className={styles.fullWidthCardContentWrapper}>
              <PrismicImage
                field={slice.primary.card5_image}
                className={styles.image}
                style={{ height: 165 }}
              />
              <div
                style={{ display: 'flex', flexDirection: 'column', gap: 16 }}
              >
                <Typography
                  variant='buttonMedium'
                  className={styles.secondaryText}
                >
                  <PrismicRichText
                    field={slice.primary.card5_subtitle}
                    components={{
                      paragraph: ({ text }) => {
                        const t = text ?? '';
                        return <span>{t.replace('{district}', context)}</span>;
                      },
                    }}
                  />
                </Typography>
                <Typography
                  variant='buttonMedium'
                  className={styles.secondaryText}
                  style={{ fontWeight: 400 }}
                >
                  <PrismicRichText field={slice.primary.card5_description} />
                </Typography>
                <Button>Get a New Boiler</Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Container>
  );
};

export default LocalAreasHeatingSection;
