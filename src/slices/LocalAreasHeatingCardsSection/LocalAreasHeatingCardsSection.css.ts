import { breakpoints } from '@/styles/constants.css';
import { theme } from '@/styles/themes.css';
import { style } from '@vanilla-extract/css';

export const container = style({
  padding: 20,
  '@media': {
    [breakpoints.tablet]: {
      padding: 60,
    },
  },
});

export const title = style({
  marginBottom: 20,
  maxWidth: 335,
  '@media': {
    [breakpoints.tablet]: {
      maxWidth: 970,
      marginBottom: 40,
    },
  },
});

export const cardsWrapper = style({
  display: 'flex',
  flexWrap: 'wrap',
  gap: 20,
  marginBottom: 40,
  '@media': {
    [breakpoints.tablet]: {
      marginBottom: 80,
      justifyContent: 'center',
    },
    [breakpoints.desktop]: {
      justifyContent: 'space-between',
    },
  },
});

export const infoWrapper = style({
  padding: 20,
  borderRadius: 24,
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.ivory,
  display: 'flex',
  justifyContent: 'space-between',
  flexWrap: 'wrap',
  gap: 20,
  '@media': {
    [breakpoints.tablet]: {
      padding: 40,
      gap: 30,
      backgroundColor: theme.colors.primary.asidGreen,
      color: theme.colors.primary.castletonGreen,
    },
  },
});

export const infoGrid = style({
  maxWidth: 570,
  display: 'grid',
  gap: '14px',
  gridTemplateColumns: '1fr',
  '@media': {
    [breakpoints.tablet]: {
      gridTemplateColumns: '1fr 1fr',
    },
  },
});

export const infoIcon = style({
  fontSize: 20,
  flexShrink: 0,
});

export const infoTitle = style({
  // width: 300,
  '@media': {
    [breakpoints.tablet]: {
      width: 600,
    },
  },
});

export const btnWrapper = style({
  display: 'flex',
  flexWrap: 'wrap',
  gap: 20,
  justifyContent: 'flex-end',
  width: '100%',
});

export const callBtn = style({
  width: '100%',
  backgroundColor: `${theme.colors.primary.softWhite} !important`,
  ':hover': {
    backgroundColor: `${theme.colors.primary.castletonGreen} !important`,
    color: theme.colors.primary.softWhite,
  },
  '@media': {
    [breakpoints.tablet]: {
      minWidth: '162px !important',
      width: 'fit-content',
    },
  },
});

export const whatsappBtn = style({
  width: '100%',
  color: `${theme.colors.primary.castletonGreen} !important`,
  backgroundColor: `${theme.colors.primary.asidGreen} !important`,
  ':hover': {
    backgroundColor: `${theme.colors.primary.softWhite} !important`,
  },

  '@media': {
    [breakpoints.tablet]: {
      color: `${theme.colors.primary.asidGreen} !important`,
      backgroundColor: `${theme.colors.primary.castletonGreen} !important`,
      width: 'fit-content',
    },
  },
});

export const whatsappIcon = style({
  color: theme.colors.primary.castletonGreen,
  '@media': {
    [breakpoints.tablet]: {
      color: theme.colors.primary.asidGreen,
    },
  },
});

export const card = style({
  width: '100%',
  padding: 24,
  borderRadius: 24,
  backgroundColor: theme.colors.primary.softWhite,
  display: 'flex',
  flexDirection: 'column',
  gap: 30,
  '@media': {
    [breakpoints.tablet]: {
      width: 297,
    },
  },
});

export const bookBtn = style({
  marginTop: 'auto',
  width: '100% !important',
  ':hover': {
    backgroundColor: theme.colors.primary.castletonGreen,
    color: theme.colors.primary.softWhite,
  },
});

export const list = style({
  padding: 0,
  paddingLeft: 0,
  display: 'flex',
  flexDirection: 'column',
  gap: 10,
  '@media': {
    [breakpoints.tablet]: {
      padding: '20px 0',
    },
  },
});

export const listItem = style({
  display: 'flex',
  gap: 14,
});

export const listItemIcon = style({
  fontSize: 20,
});

export const listItemText = style({
  fontSize: 18,
  '@media': {
    [breakpoints.tablet]: {
      fontSize: 20,
    },
  },
});
