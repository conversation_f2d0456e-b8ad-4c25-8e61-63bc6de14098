import Container from '@/components/Container';
import { Content } from '@prismicio/client';
import { PrismicRichText, SliceComponentProps } from '@prismicio/react';
import Typography from '@/components/Typography';
import MemoCheckMarkIcon from '@/assets/icons/CheckMarkIcon';
import {
  CallButtonSecondary,
  WhatsAppButtonOutlined,
} from '@/components/ContactButtons';
import VerifiedListIcon from '@/assets/icons/VerifiedListIcon.tsx';
import Hours24Icon from '@/assets/icons/Hours24Icon';
import LikeWithStarsIcon from '@/assets/icons/LikeWithStarsIcon';
import HomeWithHeart from '@/assets/icons/HomeWithHeart';
import NoHiddenCostsIcon from '@/assets/icons/NoHiddenCostsIcon';
import Button from '@/components/Button';
import * as styles from './LocalAreasHeatingCardsSection.css';

/**
 * Props for `LocalAreasHeatingCardsSection`.
 */
export type LocalAreasHeatingCardsSectionProps =
  SliceComponentProps<Content.LocalAreasHeatingCardsSectionSlice> & {
    context?: string;
  };

/**
 * Component for "LocalAreasHeatingCardsSection" Slices.
 */
const LocalAreasHeatingCardsSection = ({
  slice,
  context,
}: LocalAreasHeatingCardsSectionProps): JSX.Element => {
  return (
    <section
      data-slice-type={slice.slice_type}
      data-slice-variation={slice.variation}
    >
      <Container removeBorderRadius className={styles.container}>
        <Typography
          variant='h3'
          fontFamily='primary'
          as='h2'
          className={styles.title}
        >
          <PrismicRichText
            field={slice.primary.title}
            components={{
              paragraph: ({ text }) => {
                const t = text ?? '';
                const updatedText = t.replace('{district}', '');
                return (
                  <span>
                    {updatedText}{' '}
                    <span style={{ fontWeight: 700 }}>{context}</span>
                  </span>
                );
              },
            }}
          />
        </Typography>

        <div className={styles.cardsWrapper}>
          <div className={styles.card}>
            <Typography variant='subTitleMedium' as='h3'>
              Boiler Services in {context}
            </Typography>

            <ul className={styles.list}>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  New boiler installations
                </Typography>
              </li>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Instant online fixed-price quotes
                </Typography>
              </li>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Next-day install availability
                </Typography>
              </li>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Boiler servicing & safety checks
                </Typography>
              </li>
            </ul>

            <Button className={styles.bookBtn}>Book Now</Button>
          </div>

          <div className={styles.card}>
            <Typography variant='subTitleMedium' as='h3'>
              Heating Services in {context}
            </Typography>

            <ul className={styles.list}>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Radiator upgrades & balancing
                </Typography>
              </li>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Thermostat installation
                </Typography>
              </li>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Power flushing
                </Typography>
              </li>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Underfloor heating repairs
                </Typography>
              </li>
            </ul>

            <Button className={styles.bookBtn}>Book Now</Button>
          </div>

          <div className={styles.card}>
            <Typography variant='subTitleMedium' as='h3'>
              Landlord & Commercial Services in {context}
            </Typography>

            <ul className={styles.list}>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Gas Safety Certificates (CP12)
                </Typography>
              </li>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Plumbing maintenance contracts
                </Typography>
              </li>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Fast reactive callouts for property managers
                </Typography>
              </li>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Transparent reports for tenants & landlords
                </Typography>
              </li>
            </ul>

            <Button className={styles.bookBtn}>Book Now</Button>
          </div>

          <div className={styles.card}>
            <Typography variant='subTitleMedium' as='h3'>
              LGeneral Plumbing in {context}
            </Typography>

            <ul className={styles.list}>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Tap & toilet repairs
                </Typography>
              </li>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Shower & bathroom installs
                </Typography>
              </li>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Leak detection
                </Typography>
              </li>
              <li className={styles.listItem}>
                <MemoCheckMarkIcon className={styles.listItemIcon} />
                <Typography className={styles.listItemText}>
                  Water pressure issues
                </Typography>
              </li>
            </ul>

            <Button className={styles.bookBtn}>Book Now</Button>
          </div>
        </div>

        <div className={styles.infoWrapper}>
          <Typography
            variant='h3'
            fontFamily='primary'
            className={styles.infoTitle}
          >
            Why {context} <span style={{ fontWeight: 700 }}>Trusts</span>{' '}
            Pleasant Plumbers
          </Typography>

          <div className={styles.infoGrid}>
            <div style={{ display: 'flex', gap: 14 }}>
              <VerifiedListIcon className={styles.infoIcon} />
              <Typography>Local Experts in {context}</Typography>
            </div>
            <div style={{ display: 'flex', gap: 14 }}>
              <MemoCheckMarkIcon className={styles.infoIcon} />
              <Typography>No Hidden Costs</Typography>
            </div>
            <div style={{ display: 'flex', gap: 14 }}>
              <Hours24Icon className={styles.infoIcon} />
              <Typography>Available 24/7</Typography>
            </div>
            <div style={{ display: 'flex', gap: 14 }}>
              <LikeWithStarsIcon className={styles.infoIcon} />
              <Typography>Trusted by Londoners</Typography>
            </div>
            <div style={{ display: 'flex', gap: 14 }}>
              <NoHiddenCostsIcon className={styles.infoIcon} />
              <Typography>1 Hour Guarantee</Typography>
            </div>
            <div style={{ display: 'flex', gap: 14 }}>
              <HomeWithHeart className={styles.infoIcon} />
              <Typography>
                Respectful of Your Time, Property & Privacy
              </Typography>
            </div>
          </div>

          <div className={styles.btnWrapper}>
            <CallButtonSecondary
              isOutlineIcon
              text='Call Us'
              className={styles.callBtn}
            />
            <WhatsAppButtonOutlined
              className={styles.whatsappBtn}
              iconClassName={styles.whatsappIcon}
            />
          </div>
        </div>
      </Container>
    </section>
  );
};

export default LocalAreasHeatingCardsSection;
