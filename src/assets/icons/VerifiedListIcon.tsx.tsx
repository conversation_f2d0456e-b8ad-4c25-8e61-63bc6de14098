import * as React from 'react';

function VerifiedListIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      {...props}
    >
      <path
        d='M20 5.83317V14.1665C20 15.1823 19.63 16.1615 18.9583 16.9223C18.6525 17.269 18.1258 17.2998 17.7825 16.9957C17.4367 16.6907 17.4042 16.164 17.7083 15.819C18.1108 15.3623 18.3333 14.7757 18.3333 14.1657V5.83317C18.3333 4.45484 17.2117 3.33317 15.8333 3.33317H4.16667C2.78833 3.33317 1.66667 4.45484 1.66667 5.83317V14.1665C1.66667 15.5448 2.78833 16.6665 4.16667 16.6665H9.16667C9.6275 16.6665 10 17.0398 10 17.4998C10 17.9598 9.6275 18.3332 9.16667 18.3332H4.16667C1.86917 18.3332 0 16.464 0 14.1665V5.83317C0 3.53567 1.86917 1.6665 4.16667 1.6665H15.8333C18.1308 1.6665 20 3.53567 20 5.83317ZM5 7.49984H15C15.4608 7.49984 15.8333 7.1265 15.8333 6.6665C15.8333 6.2065 15.4608 5.83317 15 5.83317H5C4.53917 5.83317 4.16667 6.2065 4.16667 6.6665C4.16667 7.1265 4.53917 7.49984 5 7.49984ZM17.0833 13.3332C17.0833 14.379 16.5892 15.3023 15.8333 15.914V19.4557C15.8333 19.9748 15.1742 20.1973 14.8592 19.7848L13.75 18.3323L12.6408 19.7848C12.3258 20.1973 11.6667 19.9748 11.6667 19.4557V15.914C10.9108 15.3023 10.4167 14.379 10.4167 13.3332C10.4167 11.4948 11.9117 9.99984 13.75 9.99984C15.5883 9.99984 17.0833 11.4948 17.0833 13.3332ZM12.0833 13.3332C12.0833 14.2523 12.8308 14.9998 13.75 14.9998C14.6692 14.9998 15.4167 14.2523 15.4167 13.3332C15.4167 12.414 14.6692 11.6665 13.75 11.6665C12.8308 11.6665 12.0833 12.414 12.0833 13.3332ZM10 9.99984C10 9.53984 9.6275 9.1665 9.16667 9.1665H5C4.53917 9.1665 4.16667 9.53984 4.16667 9.99984C4.16667 10.4598 4.53917 10.8332 5 10.8332H9.16667C9.6275 10.8332 10 10.4598 10 9.99984ZM5 12.4998C4.53917 12.4998 4.16667 12.8732 4.16667 13.3332C4.16667 13.7932 4.53917 14.1665 5 14.1665H7.5C7.96083 14.1665 8.33333 13.7932 8.33333 13.3332C8.33333 12.8732 7.96083 12.4998 7.5 12.4998H5Z'
        fill='currentColor'
      />
    </svg>
  );
}

export default VerifiedListIcon;
