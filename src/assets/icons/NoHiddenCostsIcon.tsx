import * as React from 'react';

function NoHiddenCostsIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      {...props}
    >
      <g clip-path='url(#clip0_1_2159)'>
        <circle
          cx='10'
          cy='10'
          r='9.15'
          stroke='currentColor'
          strokeWidth='1.7'
        />
        <path d='M3 3L17 17' stroke='currentColor' strokeWidth='1.7' />
      </g>
      <defs>
        <clipPath id='clip0_1_2159'>
          <rect width='20' height='20' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
}

export default NoHiddenCostsIcon;
