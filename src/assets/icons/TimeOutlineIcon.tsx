import * as React from 'react';

function TimeOutlineIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      {...props}
    >
      <g clip-path='url(#clip0_1_2091)'>
        <path
          d='M7.5 20.0002H0.833333C0.61232 20.0002 0.400358 19.9124 0.244078 19.7561C0.0877974 19.5998 0 19.3878 0 19.1668C0 18.9458 0.0877974 18.7339 0.244078 18.5776C0.400358 18.4213 0.61232 18.3335 0.833333 18.3335H7.5C7.72101 18.3335 7.93298 18.4213 8.08926 18.5776C8.24554 18.7339 8.33333 18.9458 8.33333 19.1668C8.33333 19.3878 8.24554 19.5998 8.08926 19.7561C7.93298 19.9124 7.72101 20.0002 7.5 20.0002Z'
          fill='currentColor'
        />
        <path
          d='M5.83333 16.6667H0.833333C0.61232 16.6667 0.400358 16.5789 0.244078 16.4226C0.0877974 16.2663 0 16.0543 0 15.8333C0 15.6123 0.0877974 15.4004 0.244078 15.2441C0.400358 15.0878 0.61232 15 0.833333 15H5.83333C6.05435 15 6.26631 15.0878 6.42259 15.2441C6.57887 15.4004 6.66667 15.6123 6.66667 15.8333C6.66667 16.0543 6.57887 16.2663 6.42259 16.4226C6.26631 16.5789 6.05435 16.6667 5.83333 16.6667Z'
          fill='currentColor'
        />
        <path
          d='M4.16667 13.3332H0.833333C0.61232 13.3332 0.400358 13.2454 0.244078 13.0891C0.0877974 12.9328 0 12.7209 0 12.4998C0 12.2788 0.0877974 12.0669 0.244078 11.9106C0.400358 11.7543 0.61232 11.6665 0.833333 11.6665H4.16667C4.38768 11.6665 4.59964 11.7543 4.75592 11.9106C4.9122 12.0669 5 12.2788 5 12.4998C5 12.7209 4.9122 12.9328 4.75592 13.0891C4.59964 13.2454 4.38768 13.3332 4.16667 13.3332Z'
          fill='#003D23'
        />
        <path
          d='M10.8334 19.9627C10.6123 19.9726 10.3965 19.8942 10.2332 19.7449C10.07 19.5955 9.97278 19.3875 9.96294 19.1665C9.95311 18.9455 10.0315 18.7296 10.1808 18.5663C10.3301 18.4031 10.5382 18.3059 10.7592 18.296C12.3362 18.1518 13.8395 17.5611 15.093 16.5932C16.3464 15.6253 17.2981 14.3203 17.8366 12.8311C18.3751 11.3418 18.4781 9.72992 18.1336 8.18423C17.789 6.63853 17.0111 5.22301 15.8911 4.10347C14.771 2.98393 13.3551 2.20671 11.8093 1.86286C10.2634 1.519 8.6516 1.62275 7.16259 2.16193C5.67357 2.70112 4.36901 3.65343 3.40171 4.90732C2.43442 6.16121 1.84443 7.66477 1.70086 9.24188C1.68097 9.46201 1.57445 9.66522 1.40473 9.80681C1.235 9.9484 1.01599 10.0168 0.79586 9.99688C0.57573 9.97699 0.372518 9.87047 0.230928 9.70075C0.089338 9.53103 0.0209683 9.31201 0.0408596 9.09188C0.273267 6.52797 1.48553 4.15247 3.4253 2.45986C5.36507 0.767243 7.88289 -0.112062 10.4547 0.0049777C13.0264 0.122017 15.454 1.22638 17.232 3.08819C19.01 4.95 20.0015 7.4258 20 10.0002C20.0128 12.4988 19.0846 14.9107 17.3999 16.7561C15.7153 18.6014 13.3978 19.745 10.9084 19.9594C10.8834 19.9619 10.8575 19.9627 10.8334 19.9627Z'
          fill='currentColor'
        />
        <path
          d='M10 5C9.77901 5 9.56705 5.0878 9.41076 5.24408C9.25448 5.40036 9.16669 5.61232 9.16669 5.83333V10C9.16673 10.221 9.25456 10.4329 9.41085 10.5892L11.9109 13.0892C12.068 13.241 12.2785 13.325 12.497 13.3231C12.7155 13.3212 12.9245 13.2335 13.079 13.079C13.2335 12.9245 13.3212 12.7155 13.3231 12.497C13.325 12.2785 13.241 12.068 13.0892 11.9108L10.8334 9.655V5.83333C10.8334 5.61232 10.7456 5.40036 10.5893 5.24408C10.433 5.0878 10.221 5 10 5Z'
          fill='currentColor'
        />
      </g>
      <defs>
        <clipPath id='clip0_1_2091'>
          <rect width='20' height='20' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
}

export default TimeOutlineIcon;
