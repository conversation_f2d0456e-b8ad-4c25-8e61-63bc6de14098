import * as React from 'react';

function CheckMarkInsideGuard(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='25'
      viewBox='0 0 24 25'
      fill='none'
      {...props}
    >
      <path
        d='M12 1.5L20.217 3.326C20.674 3.428 21 3.833 21 4.302V14.289C21 16.295 19.997 18.169 18.328 19.281L12 23.5L5.672 19.281C4.002 18.168 3 16.295 3 14.29V4.302C3 3.833 3.326 3.428 3.783 3.326L12 1.5ZM16.452 8.722L11.502 13.671L8.674 10.843L7.26 12.257L11.503 16.5L17.867 10.136L16.452 8.722Z'
        fill='currentColor'
      />
    </svg>
  );
}

export default CheckMarkInsideGuard;
