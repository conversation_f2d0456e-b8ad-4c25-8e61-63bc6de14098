import * as React from 'react';

function WhatsAppIconOutline(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      {...props}
    >
      <g clip-path='url(#clip0_1_1877)'>
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M19.9579 4.00809C17.853 1.90063 15.0535 0.739517 12.0711 0.738281C5.92564 0.738281 0.924118 5.73967 0.921646 11.8868C0.920822 13.8518 1.43416 15.7701 2.40988 17.4608L0.828125 23.2383L6.73863 21.6878C8.36722 22.5762 10.2007 23.0444 12.0666 23.0449H12.0713C18.216 23.0449 23.2181 18.0431 23.2205 11.8957C23.2217 8.9165 22.063 6.1154 19.9579 4.00809ZM12.0711 21.162H12.0673C10.4045 21.1613 8.77371 20.7144 7.35071 19.8703L7.01247 19.6694L3.50508 20.5895L4.44125 17.1698L4.22084 16.8192C3.29318 15.3438 2.80333 13.6384 2.80415 11.8875C2.80608 6.77815 6.9633 2.62134 12.0748 2.62134C14.55 2.62216 16.8768 3.58731 18.6264 5.33894C20.376 7.09058 21.3389 9.41885 21.3381 11.895C21.3359 17.0048 17.1789 21.162 12.0711 21.162ZM17.1542 14.2215C16.8757 14.082 15.506 13.4083 15.2506 13.3152C14.9954 13.2222 14.8095 13.1759 14.6239 13.4547C14.4381 13.7335 13.9043 14.3611 13.7417 14.5469C13.5791 14.7328 13.4168 14.7561 13.1382 14.6166C12.8595 14.4772 11.9619 14.1829 10.8978 13.2339C10.0697 12.4952 9.51062 11.5829 9.34802 11.3041C9.1857 11.0251 9.34665 10.8888 9.47025 10.7356C9.77182 10.3611 10.0738 9.96844 10.1666 9.78264C10.2596 9.59669 10.2131 9.43396 10.1433 9.29457C10.0738 9.15518 9.51666 7.78395 9.28458 7.22598C9.05826 6.68298 8.82878 6.75632 8.65767 6.7478C8.49535 6.7397 8.30954 6.73805 8.12373 6.73805C7.93806 6.73805 7.63622 6.80768 7.38078 7.08673C7.12549 7.36565 6.40588 8.03952 6.40588 9.41075C6.40588 10.782 7.40413 12.1067 7.54338 12.2926C7.68263 12.4785 9.50787 15.2924 12.3024 16.499C12.9671 16.7863 13.4859 16.9575 13.8906 17.0859C14.558 17.298 15.1651 17.268 15.6452 17.1964C16.1806 17.1163 17.2933 16.5223 17.5257 15.8717C17.7578 15.2209 17.7578 14.6632 17.688 14.5469C17.6185 14.4307 17.4327 14.3611 17.1542 14.2215Z'
          fill='currentColor'
        />
      </g>
      <defs>
        <clipPath id='clip0_1_1877'>
          <rect width='24' height='24' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
}

const MemoWhatsAppIconOutline = React.memo(WhatsAppIconOutline);
export default MemoWhatsAppIconOutline;
