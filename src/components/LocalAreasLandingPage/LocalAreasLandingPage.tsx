import { FC } from 'react';
import classNames from 'classnames';
import { components } from '@/slices';
import FloatingContactWidgets from '../FloatingContactWidgets';
import Props from './LocalAreasLandingPage.types';
import { WHATS_UP_NUMBER, HEADER_PHONE_NUMBER } from '@/utils/constants';
import { SliceZone } from '@prismicio/react';
import HeaderSection from '../ProtectYourBusiness/components/Header/HeaderSection';
import Link from 'next/link';
import FooterLocalAreas from '../FooterLocalAreas';
import * as styles from './LocalAreasLandingPage.css';

const LocalAreasLandingPage: FC<Props> = ({
  page,
  searchParams,
  slicesData,
}) => {
  return (
    <>
      <div className={styles.root}>
        <HeaderSection
          buttonComponent={
            <Link className={styles.link} href='/become-subcontractor'>
              Message Us
            </Link>
          }
        />
        <main className={classNames(styles.main, `main-${page.uid}`)}>
          <SliceZone
            slices={
              searchParams.mode === 'commercial'
                ? page.data.slices2.length
                  ? page.data.slices2
                  : page.data.slices
                : page.data.slices
            }
            components={components}
            context={slicesData}
          />
        </main>
        <FooterLocalAreas />
      </div>
      <FloatingContactWidgets
        phoneNumber={HEADER_PHONE_NUMBER}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </>
  );
};

export default LocalAreasLandingPage;
