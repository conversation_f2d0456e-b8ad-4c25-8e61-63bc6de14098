'use client';

import React from 'react';
import classNames from 'classnames';
import Button from '@components/Button';
import { HEADER_PHONE_NUMBER } from '@/utils/constants';
import MemoPhoneIcon from '@/assets/icons/PhoneIcon';
import MemoPhoneIconOutline from '@/assets/icons/PhoneIconOutline';
import * as styles from './callButtonSecondary.css';

interface CallButtonProps {
  className?: string;
  text?: string;
  iconClassName?: string;
  phoneNumber?: string;
  isOutlineIcon?: boolean;
}

const CallButtonSecondary: React.FC<CallButtonProps> = ({
  className,
  text = 'Emergency? Call Now',
  iconClassName,
  phoneNumber = HEADER_PHONE_NUMBER,
  isOutlineIcon = false,
}) => {
  const handleClick = () => {
    window.location.href = `tel:${phoneNumber}`;
  };

  return (
    <Button
      variant='filled'
      onClick={handleClick}
      className={classNames(styles.callButton, className)}
    >
      <div className={styles.contentWrapper}>
        {isOutlineIcon ? (
          <MemoPhoneIconOutline
            className={classNames(styles.buttonIcon, iconClassName)}
          />
        ) : (
          <MemoPhoneIcon
            className={classNames(styles.buttonIcon, iconClassName)}
          />
        )}

        <span>{text}</span>
      </div>
    </Button>
  );
};

export default CallButtonSecondary;
