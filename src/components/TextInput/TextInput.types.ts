import {
  DetailedHTMLProps,
  InputHT<PERSON>Attributes,
  LegacyRef,
  ReactNode,
} from "react";

type TextInputProps = {
  variant?: "filled" | "outlined";
  element?: "input" | "textarea";
  error?: string | null;
  isError?: boolean;
  label?: ReactNode;
  containerRef?: LegacyRef<HTMLDivElement> | undefined;
  renderPreffix?: () => ReactNode;
  renderSuffix?: () => ReactNode;
  description?: string;
  inputClassname?: string
  inputContainerClassName?: string
} & DetailedHTMLProps<InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>;

export default TextInputProps;
